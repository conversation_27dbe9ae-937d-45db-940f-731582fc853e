// Instead of using req.on
export default async function handler(req, res) {
  if (req.method === 'POST') {
    try {
      // For Next.js API routes, the body is already parsed
      const data = req.body;
      
      // Process your data here
      // ...
      
      res.status(200).json({ success: true });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  } else {
    res.setHeader('Allow', ['POST']);
    res.status(405).json({ error: `Method ${req.method} not allowed` });
  }
}