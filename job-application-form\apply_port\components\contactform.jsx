"use client";
import { useState } from "react";

export default function Contactform() {
    const [fullname, setFullname] = useState("");
    const [email, setEmail] = useState("");
    const [message, setMessage] = useState("");
    const [error, setError] = useState("");

    const handleSubmit = async (e) => {
        e.preventDefault();

        console.log("Full name:", fullname);
        console.log("Email:", email);
        console.log("Message:", message);

        try {
            const res = await fetch("/api/contact", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ fullname, email, message }),
            });


            const data = await res.json();

            if (!res.ok) {
                throw new Error(data.msg || "Something went wrong");
            }

            setError(""); // clear any previous errors
            alert("Message sent successfully!");
        } catch (err) {
            setError(err.message);
        }
    };

    return (
        <form onSubmit={handleSubmit} className="py-4 mt-4 border-t flex flex-col gap-5">
            <div>
                <label htmlFor="fullname">Full Name</label>
                <input
                    onChange={(e) => setFullname(e.target.value)}
                    value={fullname}
                    type="text"
                    id="fullname"
                    placeholder="John Doe"
                />
            </div>

            <div>
                <label htmlFor="email">Email Id</label>
                <input
                    onChange={(e) => setEmail(e.target.value)}
                    value={email}
                    type="text"
                    id="email"
                    placeholder="<EMAIL>"
                />
            </div>

            <div>
                <label htmlFor="message">Message</label>
                <textarea
                    onChange={(e) => setMessage(e.target.value)}
                    value={message}
                    className="h-32"
                    id="message"
                    placeholder="Message to us"
                />
            </div>

            <button className="bg-green-700 p-3 text-white font-bold" type="submit">
                Send
            </button>

            {error && (
                <div className="bg-slate-100 flex flex-col">
                    <div className="text-red-600 px-5 py-2">{error}</div>
                </div>
            )}
        </form>
    );
}
