import mongoose, { Schema } from ("mongoose");

const contactSchema = new Schema({
    fullname: {
        type: String,
        required: [true, "Name is required"],
        trim: true,
        minLength: [2, "Name must be at least 2 characters long"],
        maxLength: [50, "Name can't be more than 50 characters"],
    },

    email: {
        type: String,
        required: [true, "Email is required"],
        match: [/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, "Invalid Email adderess"],
        minLength: [5, "Email must be at least 5 characters long"],
        maxLength: [50, "Email can't be more than 50 characters"],
    },

    message: {
        type: String,
        required: [true, "message is required"],
    },

    date: {
        type: Date,
        default: Date.now,
    }
});

const Contact = mongoose.models.Contact || mongoose.model("Contact", contactSchema)

export default Contact;