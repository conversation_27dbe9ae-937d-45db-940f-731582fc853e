// app/api/contact/route.js
import mongoose from 'mongoose';

import { NextResponse } from 'next/server';

export async function POST(req) {
    const { fullname, email, message } = await req.json();

    try{
        await connectDB();
        await Contact.create({ fullname, email, message });

        return NextRsponse.json({
            msg: ["Message sent succesfully!"], success: true
        })
    } catch(error) {
        if(error instanceof mongoose.Error.ValidationError) {
            let erroList = [];
            for(let e in error.errors){
                errorList.push(e.message);
            }

            return NextResponse.json({msg: errorList})
        }
        else {
            return NextResponse.json(error)
        }
    }

    return NextResponse.json({ msg: "Hi from contact form" });
}
